🧾 最终生成提示词（极致详细版）
请生成一个现代AI编程工具官网，严格按照以下规范执行：

## 🎨 色彩系统与配色方案
**主色调：**
- 背景色：#10161A (深蓝灰色，RGB: 16, 22, 26)
- 前景文字：#FFFFFF (纯白色) 和 #F8F9FA (浅灰白)
- 次要文字：var(--swiss-gray-light) 约 #9CA3AF

**强调色系：**
- 金色主调：#F2BC8C (暖金色)
- 金色渐变：linear-gradient(0deg, #CECFD0, #968CFF)
- 悬停金色：hover-golden 效果
- 紫蓝渐变：#B3BAFF 到 #968CFF

**功能色彩：**
- 边框色：rgba(255,255,255,0.1) 半透明白色边框
- 卡片背景：rgba(255,255,255,0.05) 半透明白色背景
- 按钮渐变：from-[#84888A] to-[#DBD7D5] (银灰渐变)
- 悬停边框：#f2bc8c (暖金色)

**渐变背景：**
- Hero区域：bg-linear-to-b from-[var(--neutral-gray-dark)] to-[var(--neutral-gray)]
- 装饰渐变：bg-gradient-radial-purple (径向紫色渐变)
- 按钮渐变：bg-linear-to-b from-[#84888A] to-[#DBD7D5]

## 🔤 字体系统详细规范
**字体家族层级：**
- 主标题字体：Berkeley Mono, monospace (等宽字体，科技感)
- 正文字体：Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif
- 代码字体：JetBrains Mono, Consolas, monospace
- 装饰字体：var(--font-berkeley-mono) 用于特殊标题

**字号与字重系统：**
- h1大标题：text-4xl md:text-6xl, font-bold (48px-72px)
- h6副标题：text-lg md:text-xl, font-medium (18px-24px)
- body正文：text-sm md:text-base, font-normal (14px-16px)
- 小字说明：text-xs, font-normal (12px)
- 按钮文字：text-sm, font-medium (14px)

**行高与间距：**
- 标题行高：leading-tight (1.25)
- 正文行高：leading-relaxed (1.625)
- 字母间距：tracking-normal
- 抗锯齿：antialiased

## 🏗️ 布局结构与组件规范

**整体布局：**
```html
<html data-theme="dark" class="bg-[#10161A]">
  <body class="font-sans antialiased text-foreground">
    <nav class="fixed top-0 z-50 backdrop-blur-sm">
    <main class="min-h-screen pt-20 md:pt-28">
    <footer class="bg-background">
导航栏详细规范：

位置：fixed top-0 right-0 left-0 z-50
背景：backdrop-blur-sm (毛玻璃效果)
容器：max-w-5xl mx-auto px-3 py-2
Logo尺寸：width="151" height="26"
菜单项间距：gap-4
按钮组：gap-3 flex-col md:flex-row
响应式：md:hidden 汉堡菜单
Hero区域规范：

背景：overflow-hidden bg-linear-to-b 渐变
容器：container mx-auto max-w-5xl
标题动画：opacity:0;filter:blur(10px);transform:translateY(30px)
间距：gap-8 pb-16
卡片组件规范：

基础样式：rounded-xl border shadow-sm
背景：bg-secondary border-frame-lg
悬停效果：hover:border-[#f2bc8c] transition-all duration-300
内边距：p-2 内容区 px-4
阴影：backdrop-blur-sm
🎭 视觉风格与设计语言
设计风格定位：

现代科技风 (Modern Tech)：深色背景+金属质感
极简主义 (Minimalism)：大量留白+简洁布局
专业工具感 (Professional Tool)：代码编辑器美学
未来感 (Futuristic)：渐变+光效+动画
视觉层次：

主要内容：高对比度白色文字
次要信息：中等对比度灰色文字
装饰元素：低对比度半透明效果
交互元素：金色强调突出
圆角系统：

小圆角：rounded-md (6px)
中圆角：rounded-lg (8px)
大圆角：rounded-xl (12px)
完全圆角：rounded-full
⚡ 动画与交互效果
入场动画：

初始状态：opacity:0;filter:blur(10px);transform:translateY(30px)
最终状态：opacity:1;filter:blur(0);transform:translateY(0)
过渡：transition-all duration-500 ease-out
悬停效果系统：

文字悬停：hover:text-white transition-colors duration-200
按钮悬停：hover-golden (金色光效)
卡片悬停：hover:border-[#f2bc8c] hover:shadow-lg
图标悬停：glow-gold-hover (金色光晕)
旋转动画：

@keyframes spin-slow {
  from { transform: rotate(0deg) }
  to { transform: rotate(360deg) }
}
animation: spin-slow 4s linear infinite
光效动画：

径向渐变旋转
圆锥渐变效果：conic-gradient(from 0deg, #ffffff80, #fff0 60deg)
混合模式：mix-blend-mode: hard-light
📱 响应式断点系统
断点定义：

移动端：默认 (< 768px)
平板端：md: (≥ 768px)
桌面端：lg: (≥ 1024px)
大屏幕：xl: (≥ 1280px)
响应式组件行为：

导航：移动端折叠菜单，桌面端水平布局
网格：grid-cols-4 lg:grid-cols-8
文字：text-4xl md:text-6xl
间距：gap-4 md:gap-8
按钮：flex-col md:flex-row
🧩 具体组件实现规范
按钮组件变体：

<!-- 主按钮 -->
<button class="bg-linear-to-b from-[#84888A] to-[#DBD7D5] text-foreground-emphasis border-border-highlight hover-golden h-9 px-4 rounded-md">

<!-- 次要按钮 -->
<button class="text-foreground border-border hover-golden h-9 px-4 rounded-md">

<!-- 图标按钮 -->
<button class="size-9 rounded-md hover:bg-accent">
卡片组件结构：

<div class="bg-secondary border-frame-lg rounded-xl p-2 hover:border-[#f2bc8c] transition-all duration-300">
  <div class="card-header">
  <div class="card-content px-4">
  <div class="card-footer px-4">
网格系统：

<div class="grid grid-cols-4 lg:grid-cols-8 border-border/20 bg-gradient-to-b from-background/5 to-background/20">
  <div class="aspect-square border-r border-b hover:glow-gold-hover">
🎯 特殊效果与装饰
毛玻璃效果：

backdrop-blur-sm (4px模糊)
半透明背景色
边框增强视觉层次
光晕效果：

box-shadow: 0 0 20px rgba(242, 188, 140, 0.3)
悬停时透明度变化
径向渐变背景
边框装饰：

虚线边框：border-dashed border-gray-700
渐变边框：border-linear
动态边框色：hover:border-[#f2bc8c]
背景纹理：

像素点阵图案：footer-pixels-*.svg
噪点纹理效果
渐变叠加
🔧 技术实现要求
框架选择：

Next.js 14+ (App Router)
React 18+
Tailwind CSS 3.4+
TypeScript
性能优化：

字体预加载：
图片优化：Next.js Image组件
代码分割：动态导入
CSS优化：PurgeCSS
无障碍支持：

语义化HTML标签
ARIA属性完整
键盘导航支持
屏幕阅读器友好
色彩对比度符合WCAG 2.1 AA标准
SEO优化：

完整meta标签
Open Graph协议
Twitter Cards
结构化数据
使用以上详细规范生成一个专业的AI编程工具官网，确保每个细节都符合现代Web设计标准和用户体验最佳实践。


这个极致详细的提示词包含了：
- 精确的颜色值和配色方案
- 完整的字体系统规范  
- 详细的布局和组件结构
- 具体的动画和交互效果
- 响应式设计断点
- 技术实现要求
- 性能和无障碍优化

可以直接用于AI工具生成高质量的网站界面。