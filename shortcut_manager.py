#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快捷键管理模块 - 负责全局快捷键的注册和响应
"""

import keyboard
import pyperclip
import webbrowser
from typing import Dict, Any
import threading
import time


class ShortcutManager:
    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.registered_hotkeys = []
        self.is_listening = False
        
    def register_hotkeys(self):
        """注册所有快捷键"""
        # 先清除已注册的快捷键
        self.clear_hotkeys()
        
        config_data = self.data_manager.get_config()
        
        for config in config_data:
            try:
                hotkey = config["hotkey"]
                action_type = config["type"]
                content = config["content"]
                description = config.get("description", "")
                
                # 使用 lambda 捕获当前循环变量
                callback = self._create_callback(action_type, content, description, config)
                
                # 注册快捷键
                keyboard.add_hotkey(hotkey, callback)
                self.registered_hotkeys.append(hotkey)
                
                print(f"✅ 注册快捷键: {hotkey} -> {description}")
                
            except Exception as e:
                print(f"❌ 注册快捷键失败 {config.get('hotkey', 'unknown')}: {e}")
    
    def _create_callback(self, action_type: str, content: str, description: str, config: dict = None):
        """创建快捷键回调函数"""
        def callback():
            try:
                if action_type == "prompt":
                    # 复制提示词到剪贴板
                    pyperclip.copy(content)
                    print(f"📋 已复制提示词: {description}")
                    
                elif action_type == "url":
                    # 打开网页
                    open_mode = config.get("open_mode", "tab") if config else "tab"  # 默认在标签页打开
                    if open_mode == "window":
                        webbrowser.open(content, new=1)  # 新窗口
                        print(f"🌐 已在新窗口打开网页: {description}")
                    else:
                        webbrowser.open(content, new=2)  # 新标签页
                        print(f"🌐 已在新标签页打开网页: {description}")
                        
                elif action_type == "combo":
                    # 组合操作：打开网页 + 自动输入提示词
                    combo_url = config.get("combo_url", content) if config else content
                    combo_prompt = config.get("combo_prompt", "") if config else ""
                    combo_delay = config.get("combo_delay", 2.0) if config else 2.0
                    open_mode = config.get("open_mode", "tab") if config else "tab"
                    
                    # 先打开网页
                    if open_mode == "window":
                        webbrowser.open(combo_url, new=1)  # 新窗口
                        print(f"🌐 已在新窗口打开网页: {combo_url}")
                    else:
                        webbrowser.open(combo_url, new=2)  # 新标签页
                        print(f"🌐 已在新标签页打开网页: {combo_url}")
                    
                    # 延迟后自动输入提示词
                    if combo_prompt:
                        def delayed_input():
                            time.sleep(combo_delay)
                            try:
                                # 复制提示词到剪贴板
                                pyperclip.copy(combo_prompt)
                                print(f"📋 已复制提示词到剪贴板: {combo_prompt[:50]}...")
                                print(f"💡 提示：请在网页中按 Ctrl+V 粘贴提示词")
                            except Exception as e:
                                print(f"❌ 自动输入提示词失败: {e}")
                        
                        # 在新线程中执行延迟操作
                        threading.Thread(target=delayed_input, daemon=True).start()
                        print(f"⏰ 将在 {combo_delay} 秒后自动复制提示词: {description}")
                    
                else:
                    print(f"⚠️ 未知操作类型: {action_type}")
                    
            except Exception as e:
                print(f"❌ 执行快捷键操作失败: {e}")
        
        return callback
    
    def clear_hotkeys(self):
        """清除所有已注册的快捷键"""
        try:
            for hotkey in self.registered_hotkeys:
                keyboard.remove_hotkey(hotkey)
            self.registered_hotkeys.clear()
            print("🧹 已清除所有快捷键")
        except Exception as e:
            print(f"❌ 清除快捷键失败: {e}")
    
    def start_listening(self):
        """开始监听快捷键"""
        if self.is_listening:
            return
            
        self.is_listening = True
        self.register_hotkeys()
        
        print("👂 开始监听全局快捷键...")
        print("按 Ctrl+C 退出监听")
        
        try:
            # 保持监听状态
            while self.is_listening:
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n⏹️ 停止监听快捷键")
        finally:
            self.stop_listening()
    
    def stop_listening(self):
        """停止监听快捷键"""
        self.is_listening = False
        self.clear_hotkeys()
        print("⏹️ 快捷键监听已停止")
    
    def reload_hotkeys(self):
        """重新加载快捷键配置"""
        print("🔄 重新加载快捷键配置...")
        self.data_manager.load_config()
        if self.is_listening:
            self.register_hotkeys()
    
    def test_hotkey(self, hotkey: str) -> bool:
        """测试快捷键格式是否有效"""
        try:
            # 尝试解析快捷键格式
            keyboard.parse_hotkey(hotkey)
            return True
        except Exception as e:
            print(f"❌ 快捷键格式无效 '{hotkey}': {e}")
            return False