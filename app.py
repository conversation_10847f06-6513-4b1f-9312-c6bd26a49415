#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HotKey AI Web 版本 - Flask 应用主文件
"""

from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import threading
import webbrowser
import time
import os

from data_manager import DataManager
from shortcut_manager import ShortcutManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'hotkey_ai_secret_key'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局变量
data_manager = DataManager()
shortcut_manager = ShortcutManager(data_manager)
shortcut_thread = None


@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')


@app.route('/api/config', methods=['GET'])
def get_config():
    """获取配置"""
    return jsonify({
        'success': True,
        'data': data_manager.get_config()
    })


@app.route('/api/config', methods=['POST'])
def add_config():
    """添加配置"""
    data = request.json
    hotkey = data.get('hotkey', '').strip()
    action_type = data.get('type', '')
    content = data.get('content', '').strip()
    description = data.get('description', '').strip()
    open_mode = data.get('open_mode', 'tab')
    
    # 组合操作的额外参数
    combo_kwargs = {}
    if action_type == 'combo':
        combo_kwargs['combo_url'] = data.get('combo_url', '').strip()
        combo_kwargs['combo_prompt'] = data.get('combo_prompt', '').strip()
        combo_kwargs['combo_delay'] = data.get('combo_delay', 2.0)
    
    if not hotkey or not content:
        return jsonify({'success': False, 'message': '快捷键和内容不能为空'})
    
    # 验证快捷键格式
    if not shortcut_manager.test_hotkey(hotkey):
        return jsonify({'success': False, 'message': '快捷键格式无效'})
    
    if data_manager.add_config(hotkey, action_type, content, description, open_mode, **combo_kwargs):
        reload_shortcuts()
        socketio.emit('config_updated', {'action': 'add', 'data': data_manager.get_config()})
        return jsonify({'success': True, 'message': '添加成功'})
    else:
        return jsonify({'success': False, 'message': '快捷键已存在'})


@app.route('/api/config/<int:index>', methods=['PUT'])
def update_config(index):
    """更新配置"""
    data = request.json
    hotkey = data.get('hotkey', '').strip()
    action_type = data.get('type', '')
    content = data.get('content', '').strip()
    description = data.get('description', '').strip()
    open_mode = data.get('open_mode', 'tab')
    
    # 组合操作的额外参数
    combo_kwargs = {}
    if action_type == 'combo':
        combo_kwargs['combo_url'] = data.get('combo_url', '').strip()
        combo_kwargs['combo_prompt'] = data.get('combo_prompt', '').strip()
        combo_kwargs['combo_delay'] = data.get('combo_delay', 2.0)
    
    if not hotkey or not content:
        return jsonify({'success': False, 'message': '快捷键和内容不能为空'})
    
    # 验证快捷键格式
    if not shortcut_manager.test_hotkey(hotkey):
        return jsonify({'success': False, 'message': '快捷键格式无效'})
    
    if data_manager.update_config(index, hotkey, action_type, content, description, open_mode, **combo_kwargs):
        reload_shortcuts()
        socketio.emit('config_updated', {'action': 'update', 'data': data_manager.get_config()})
        return jsonify({'success': True, 'message': '更新成功'})
    else:
        return jsonify({'success': False, 'message': '快捷键冲突或更新失败'})


@app.route('/api/config/<int:index>', methods=['DELETE'])
def delete_config(index):
    """删除配置"""
    if data_manager.delete_config(index):
        reload_shortcuts()
        socketio.emit('config_updated', {'action': 'delete', 'data': data_manager.get_config()})
        return jsonify({'success': True, 'message': '删除成功'})
    else:
        return jsonify({'success': False, 'message': '删除失败'})


@app.route('/api/test/<int:index>', methods=['POST'])
def test_config(index):
    """测试配置"""
    config_data = data_manager.get_config()
    if 0 <= index < len(config_data):
        config = config_data[index]
        try:
            if config["type"] == "prompt":
                import pyperclip
                pyperclip.copy(config["content"])
                return jsonify({'success': True, 'message': f'提示词已复制到剪贴板'})
            elif config["type"] == "url":
                open_mode = config.get("open_mode", "tab")
                if open_mode == "window":
                    webbrowser.open(config["content"], new=1)
                    return jsonify({'success': True, 'message': f'已在新窗口打开网页'})
                else:
                    webbrowser.open(config["content"], new=2)
                    return jsonify({'success': True, 'message': f'已在新标签页打开网页'})
            elif config["type"] == "combo":
                combo_url = config.get("combo_url", config["content"])
                combo_prompt = config.get("combo_prompt", "")
                combo_delay = config.get("combo_delay", 2.0)
                open_mode = config.get("open_mode", "tab")
                
                # 打开网页
                if open_mode == "window":
                    webbrowser.open(combo_url, new=1)
                else:
                    webbrowser.open(combo_url, new=2)
                
                # 延迟复制提示词
                if combo_prompt:
                    def delayed_copy():
                        time.sleep(combo_delay)
                        import pyperclip
                        pyperclip.copy(combo_prompt)
                    
                    threading.Thread(target=delayed_copy, daemon=True).start()
                    return jsonify({'success': True, 'message': f'已打开网页，{combo_delay}秒后将复制提示词到剪贴板'})
                else:
                    return jsonify({'success': True, 'message': f'已打开网页'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'测试失败: {str(e)}'})
    
    return jsonify({'success': False, 'message': '配置不存在'})


@app.route('/api/reload', methods=['POST'])
def reload_config():
    """重新加载配置"""
    data_manager.load_config()
    reload_shortcuts()
    socketio.emit('config_updated', {'action': 'reload', 'data': data_manager.get_config()})
    return jsonify({'success': True, 'message': '配置已重载'})


@socketio.on('connect')
def handle_connect():
    """客户端连接"""
    print('客户端已连接')
    emit('status', {'message': '✅ 已连接到服务器'})


@socketio.on('disconnect')
def handle_disconnect():
    """客户端断开连接"""
    print('客户端已断开连接')


def reload_shortcuts():
    """重新加载快捷键"""
    global shortcut_thread
    
    # 停止当前监听
    shortcut_manager.stop_listening()
    
    # 重新启动监听线程
    if shortcut_thread and shortcut_thread.is_alive():
        shortcut_thread.join(timeout=1)
    
    shortcut_thread = threading.Thread(target=start_shortcut_listener, daemon=True)
    shortcut_thread.start()


def start_shortcut_listener():
    """启动快捷键监听"""
    try:
        shortcut_manager.start_listening()
    except Exception as e:
        print(f"快捷键监听错误: {e}")
        socketio.emit('error', {'message': f'快捷键监听错误: {str(e)}'})


def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')


if __name__ == '__main__':
    print("🚀 启动 HotKey AI Web 版本...")
    
    # 启动快捷键监听
    shortcut_thread = threading.Thread(target=start_shortcut_listener, daemon=True)
    shortcut_thread.start()
    
    # 延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    # 启动 Flask 应用
    socketio.run(app, host='127.0.0.1', port=5000, debug=False)