#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HotKey AI - 快捷键提示词助手
主入口文件 - 默认启动 Web 界面
"""

import sys
import os
import argparse
import subprocess
import importlib.util


def check_and_install_dependencies():
    """检查并自动安装缺失的依赖"""
    print("🔍 检查依赖包...")
    
    # 读取 requirements.txt
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print("❌ 未找到 requirements.txt 文件")
        return False
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except Exception as e:
        print(f"❌ 读取 requirements.txt 失败: {e}")
        return False
    
    missing_packages = []
    
    # 检查每个依赖包
    for requirement in requirements:
        # 解析包名（去掉版本号）
        package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0].strip()
        
        # 特殊处理一些包名映射
        import_name = package_name
        if package_name == 'flask-socketio':
            import_name = 'flask_socketio'
        elif package_name == 'python-socketio':
            import_name = 'socketio'
        
        # 检查包是否已安装
        try:
            spec = importlib.util.find_spec(import_name)
            if spec is None:
                missing_packages.append(requirement)
            else:
                print(f"✅ {package_name} 已安装")
        except ImportError:
            missing_packages.append(requirement)
    
    # 如果有缺失的包，自动安装
    if missing_packages:
        print(f"📦 发现 {len(missing_packages)} 个缺失的依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        
        print("🔧 正在自动安装缺失的依赖...")
        
        try:
            # 使用当前 Python 解释器安装依赖
            cmd = [sys.executable, "-m", "pip", "install"] + missing_packages
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                print("✅ 依赖安装成功！")
                return True
            else:
                print(f"❌ 依赖安装失败:")
                print(f"错误信息: {result.stderr}")
                print("💡 请手动运行: pip install -r requirements.txt")
                return False
                
        except Exception as e:
            print(f"❌ 自动安装失败: {e}")
            print("💡 请手动运行: pip install -r requirements.txt")
            return False
    else:
        print("✅ 所有依赖都已安装")
        return True

def run_web_mode():
    """运行 Web 模式 - 默认模式"""
    try:
        from app import socketio, app
        print("🚀 启动 HotKey AI (Web 模式)...")
        print("🌐 浏览器将自动打开 http://127.0.0.1:5000")
        print("📝 在浏览器中管理你的快捷键配置")
        print("⌨️ 快捷键会在后台全局监听")
        print("🛑 按 Ctrl+C 停止服务")
        print("-" * 50)
        
        socketio.run(app, host='127.0.0.1', port=5000, debug=False)
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请先安装依赖: pip install -r requirements.txt")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")



if __name__ == "__main__":
    # 首先检查并安装依赖
    print("=" * 50)
    print("🎯 HotKey AI - 快捷键提示词助手")
    print("=" * 50)
    
    if not check_and_install_dependencies():
        print("\n❌ 依赖检查失败，程序无法启动")
        input("按回车键退出...")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 启动 Web 模式
    run_web_mode()