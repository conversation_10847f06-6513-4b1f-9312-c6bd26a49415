#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据管理模块 - 负责配置文件的读取和保存
"""

import json
import os
from typing import List, Dict, Any


class DataManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config_data = []
        self.load_config()
    
    def load_config(self) -> List[Dict[str, Any]]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                print(f"✅ 成功加载配置文件，共 {len(self.config_data)} 个快捷键")
            else:
                print("⚠️ 配置文件不存在，使用默认配置")
                self.config_data = self._get_default_config()
                self.save_config()
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            self.config_data = self._get_default_config()
        
        return self.config_data
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            print("✅ 配置文件保存成功")
            return True
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    def get_config(self) -> List[Dict[str, Any]]:
        """获取当前配置"""
        return self.config_data
    
    def add_config(self, hotkey: str, action_type: str, content: str, description: str = "", open_mode: str = "tab", **kwargs) -> bool:
        """添加新的快捷键配置"""
        # 检查快捷键是否已存在
        if self.hotkey_exists(hotkey):
            return False
        
        new_config = {
            "hotkey": hotkey,
            "type": action_type,
            "content": content,
            "description": description
        }
        
        # 根据类型添加特定字段
        if action_type == "url":
            new_config["open_mode"] = open_mode
        elif action_type == "combo":
            new_config["open_mode"] = open_mode
            new_config["combo_url"] = kwargs.get("combo_url", content)
            new_config["combo_prompt"] = kwargs.get("combo_prompt", "")
            new_config["combo_delay"] = kwargs.get("combo_delay", 2.0)
        
        self.config_data.append(new_config)
        return self.save_config()
    
    def update_config(self, index: int, hotkey: str, action_type: str, content: str, description: str = "", open_mode: str = "tab", **kwargs) -> bool:
        """更新指定索引的配置"""
        if 0 <= index < len(self.config_data):
            # 检查新快捷键是否与其他配置冲突（除了当前配置）
            for i, config in enumerate(self.config_data):
                if i != index and config["hotkey"] == hotkey:
                    return False
            
            new_config = {
                "hotkey": hotkey,
                "type": action_type,
                "content": content,
                "description": description
            }
            
            # 根据类型添加特定字段
            if action_type == "url":
                new_config["open_mode"] = open_mode
            elif action_type == "combo":
                new_config["open_mode"] = open_mode
                new_config["combo_url"] = kwargs.get("combo_url", content)
                new_config["combo_prompt"] = kwargs.get("combo_prompt", "")
                new_config["combo_delay"] = kwargs.get("combo_delay", 2.0)
            
            self.config_data[index] = new_config
            return self.save_config()
        return False
    
    def delete_config(self, index: int) -> bool:
        """删除指定索引的配置"""
        if 0 <= index < len(self.config_data):
            del self.config_data[index]
            return self.save_config()
        return False
    
    def hotkey_exists(self, hotkey: str) -> bool:
        """检查快捷键是否已存在"""
        return any(config["hotkey"] == hotkey for config in self.config_data)
    
    def _get_default_config(self) -> List[Dict[str, Any]]:
        """获取默认配置"""
        return [
            {
                "hotkey": "ctrl+shift+1",
                "type": "prompt",
                "content": "请帮我用一句话总结下面内容：",
                "description": "内容总结"
            },
            {
                "hotkey": "ctrl+shift+2",
                "type": "url",
                "content": "https://chat.openai.com",
                "description": "打开ChatGPT"
            }
        ]