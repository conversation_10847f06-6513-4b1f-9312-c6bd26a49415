<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HotKey AI - 智能快捷键助手</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS变量 */
        :root {
            --bg-primary: #10161A;
            --bg-secondary: rgba(255, 255, 255, 0.05);
            --text-primary: #FFFFFF;
            --text-secondary: #F8F9FA;
            --text-muted: #9CA3AF;
            --golden: #F2BC8C;
            --border-primary: rgba(255, 255, 255, 0.1);
            --border-highlight: rgba(255, 255, 255, 0.2);
            --success: #22C55E;
            --error: #EF4444;
            --warning: #F59E0B;
            --info: #3B82F6;
            --purple: #A855F7;
        }

        /* 基础样式 - 极致压缩 */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            line-height: 1.2;
            font-size: 0.75rem;
        }

        /* 导航栏 - 极致压缩 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            backdrop-filter: blur(8px);
            background: rgba(16, 22, 26, 0.98);
            border-bottom: 1px solid var(--border-primary);
            padding: 0.25rem 0;
            height: 1.75rem;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .nav-logo {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--golden);
        }

        .nav-subtitle {
            font-size: 0.625rem;
            color: var(--text-muted);
        }

        /* 导航栏右侧 */
        .nav-right {
            display: flex;
            align-items: center;
            gap: 0.375rem;
        }

        /* 导航栏提示容器 */
        .nav-alert-container {
            display: flex;
            align-items: center;
        }

        /* 导航栏提示样式 - 压缩 */
        .nav-alert {
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-weight: 500;
            font-size: 0.625rem;
            border: 1px solid;
            white-space: nowrap;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            animation: slideInRight 0.2s ease-out;
        }

        .nav-alert-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: var(--success);
        }

        .nav-alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--error);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* 状态指示器 - 压缩 */
        .status-indicator {
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-weight: 500;
            font-size: 0.625rem;
            border: 1px solid var(--border-primary);
            background: var(--bg-secondary);
            transition: all 0.2s ease;
        }

        .status-connected {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: var(--success);
        }

        .status-disconnected {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--error);
        }

        /* 主容器 - 极致压缩 */
        .main-container {
            min-height: 100vh;
            padding-top: 1.75rem;
            background: var(--bg-primary);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 0.5rem;
        }

        /* Hero区域 - 压缩 */
        .hero {
            text-align: center;
            padding: 0.5rem 0;
            animation: fadeIn 0.5s ease-out;
        }

        .hero-title {
            font-size: 1rem;
            font-weight: 600;
            line-height: 1.1;
            margin-bottom: 0.25rem;
            background: linear-gradient(135deg, var(--golden), var(--purple));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 0.625rem;
            color: var(--text-muted);
            max-width: 400px;
            margin: 0 auto;
        }

        /* 卡片组件 - 极致压缩 */
        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 0.25rem;
            backdrop-filter: blur(8px);
            transition: all 0.2s ease;
            margin-bottom: 0.5rem;
        }

        .card:hover {
            border-color: var(--golden);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            padding: 0.5rem 0.5rem 0;
        }

        .card-body {
            padding: 0.5rem;
        }

        .card-title {
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .card-title .icon {
            color: var(--golden);
        }

        /* 表单样式 - 极致压缩 */
        .form-group {
            margin-bottom: 0.375rem;
        }

        .form-label {
            display: block;
            font-size: 0.625rem;
            font-weight: 500;
            margin-bottom: 0.125rem;
            color: var(--text-secondary);
        }

        .form-input {
            width: 100%;
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            color: var(--text-primary);
            border-radius: 0.25rem;
            padding: 0.25rem 0.375rem;
            font-size: 0.625rem;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--golden);
            box-shadow: 0 0 0 3px rgba(242, 188, 140, 0.1);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        .form-textarea {
            min-height: auto;
            height: auto;
            resize: vertical;
        }

        .form-help {
            font-size: 0.5rem;
            color: var(--text-muted);
            margin-top: 0.125rem;
        }

        /* 美化下拉菜单 */
        select.form-input {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23F2BC8C' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            appearance: none;
            cursor: pointer;
        }

        select.form-input:hover {
            border-color: var(--golden);
            background-color: rgba(242, 188, 140, 0.05);
        }

        select.form-input:focus {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23F2BC8C' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
        }

        /* 下拉菜单选项样式 */
        select.form-input option {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 0.5rem;
            border: none;
        }

        select.form-input option:hover,
        select.form-input option:checked {
            background: rgba(242, 188, 140, 0.1);
            color: var(--golden);
        }

        /* 按钮样式 - 极致压缩 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.625rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 0.25rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #84888A, #DBD7D5);
            color: var(--text-primary);
            border-color: var(--border-highlight);
        }

        .btn-primary:hover {
            border-color: var(--golden);
            box-shadow: 0 0 20px rgba(242, 188, 140, 0.3);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border-color: var(--border-primary);
        }

        .btn-secondary:hover {
            border-color: var(--golden);
            background: rgba(242, 188, 140, 0.1);
        }

        .btn-small {
            padding: 0.125rem 0.25rem;
            font-size: 0.5rem;
        }

        .btn-danger {
            border-color: var(--error);
            color: var(--error);
        }

        .btn-danger:hover {
            background: rgba(239, 68, 68, 0.1);
        }

        /* 筛选按钮激活状态 */
        .filter-btn.active {
            background: rgba(242, 188, 140, 0.2);
            border-color: var(--golden);
            color: var(--golden);
        }

        /* 按钮组 */
        .btn-group {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .btn-group-vertical {
            flex-direction: column;
        }

        /* 输入组 */
        .input-group {
            display: flex;
            gap: 0.5rem;
        }

        /* 表单行布局 - 压缩 */
        .form-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1.5fr;
            gap: 0.375rem;
            align-items: end;
        }

        .form-row .form-group {
            margin-bottom: 0;
        }

        .form-row .input-group {
            align-items: stretch;
        }

        /* 警告框 - 压缩 */
        .alert {
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
            font-weight: 500;
            font-size: 0.625rem;
            margin-bottom: 0.75rem;
            border: 1px solid;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: var(--success);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: var(--error);
        }

        /* 配置项 - 极致压缩 */
        .config-item {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
            padding: 0.375rem;
            transition: all 0.2s ease;
        }

        .config-item:hover {
            border-color: var(--golden);
        }

        /* 配置项第一行 - 压缩 */
        .config-row-1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .config-info {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            flex: 1;
        }

        .config-hotkey {
            font-size: 0.625rem;
            font-weight: 600;
            color: var(--golden);
            font-family: 'JetBrains Mono', monospace;
            min-width: fit-content;
        }

        .config-description {
            color: var(--text-muted);
            font-size: 0.5rem;
            flex: 1;
            margin: 0 0.25rem;
        }

        .config-actions {
            display: flex;
            gap: 0.125rem;
            align-items: center;
        }

        /* 配置项第二行 - 压缩 */
        .config-row-2 {
            display: block;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 0.25rem;
            padding: 0.25rem;
            margin-top: 0.25rem;
        }

        .config-content {
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: var(--text-secondary);
            line-height: 1.2;
        }

        .config-meta {
            color: var(--text-muted);
            font-size: 0.5rem;
            margin-top: 0.125rem;
        }

        /* 类型标签 - 极致压缩 */
        .type-badge {
            display: inline-block;
            padding: 0.125rem 0.375rem;
            border-radius: 0.5rem;
            font-size: 0.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            border: 1px solid;
        }

        .type-badge.prompt {
            background: rgba(34, 197, 94, 0.2);
            color: var(--success);
            border-color: rgba(34, 197, 94, 0.3);
        }

        .type-badge.url {
            background: rgba(59, 130, 246, 0.2);
            color: var(--info);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .type-badge.combo {
            background: rgba(168, 85, 247, 0.2);
            color: var(--purple);
            border-color: rgba(168, 85, 247, 0.3);
        }

        /* 空状态 - 极致压缩 */
        .empty-state {
            text-align: center;
            padding: 1rem 0.75rem;
            color: var(--text-muted);
        }

        .empty-state h3 {
            font-size: 0.75rem;
            margin-bottom: 0.375rem;
            color: var(--text-secondary);
        }

        /* 动画 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                filter: blur(10px);
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                filter: blur(0);
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-subtitle {
                display: none;
            }

            .btn-group {
                flex-direction: column;
            }

            .config-actions {
                justify-content: stretch;
            }

            .config-actions .btn {
                flex: 1;
            }

            .input-group {
                flex-direction: column;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 0.25rem;
            }

            .form-row .form-group {
                margin-bottom: 0.375rem;
            }
        }

        /* 滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(242, 188, 140, 0.5);
        }

        /* 额外的极致压缩样式 */
        .btn-group {
            gap: 0.125rem;
        }

        .input-group {
            gap: 0.25rem;
        }

        /* 压缩textarea高度 */
        .form-textarea {
            min-height: 2rem;
            max-height: 4rem;
        }

        /* 压缩按钮文字 */
        .btn {
            white-space: nowrap;
        }

        /* 更紧凑的配置项布局 */
        .config-item {
            font-size: 0.625rem;
        }

        /* 压缩筛选按钮 */
        .filter-btn {
            padding: 0.125rem 0.375rem;
            font-size: 0.5rem;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="nav-logo">⚡ HotKey AI</div>
                <div class="nav-subtitle">智能快捷键助手</div>
            </div>
            <div class="nav-right">
                <div id="nav-alert-container" class="nav-alert-container"></div>
                <div id="status" class="status-indicator status-disconnected">
                    🔄 正在连接服务器...
                </div>
            </div>
        </div>
    </nav>

    <!-- 主容器 -->
    <main class="main-container">
        <div class="container">
            <!-- Hero区域 - 压缩 -->
            <div class="hero">
                <h1 class="hero-title">⚡ HotKey AI - 快捷键管理</h1>
            </div>



            <!-- 添加配置表单 -->
            <div class="card animate-slide-up">
                <div class="card-header">
                    <h2 class="card-title">
                        <span class="icon">➕</span>
                        添加新配置
                    </h2>
                </div>
                <div class="card-body">
                    <form id="add-form">
                        <!-- 快捷键、类型、描述一行 -->
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">快捷键 *</label>
                                <div class="input-group">
                                    <input type="text" id="hotkey" name="hotkey" class="form-input"
                                        placeholder="例如: ctrl+shift+a" required>
                                    <button type="button" id="record-btn" class="btn btn-secondary"
                                        onclick="toggleRecording()">
                                        🎤
                                    </button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">类型 *</label>
                                <select id="type" name="type" class="form-input" required>
                                    <option value="prompt">提示词</option>
                                    <option value="url">网页链接</option>
                                    <option value="combo">组合操作</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">描述</label>
                                <input type="text" id="description" name="description" class="form-input"
                                    placeholder="可选描述">
                            </div>
                        </div>
                        <div class="form-help" style="margin-bottom: 0.375rem;">
                            支持组合键: ctrl+shift+a, alt+f1。点击🎤录制。
                        </div>

                        <!-- 内容输入 -->
                        <div class="form-group" id="content-group">
                            <label class="form-label">内容 *</label>
                            <textarea id="content" name="content" class="form-input form-textarea"
                                placeholder="输入提示词内容或网页链接..." required></textarea>
                        </div>

                        <!-- 打开方式（URL和组合类型） -->
                        <div class="form-group" id="open-mode-group" style="display: none;">
                            <label class="form-label">打开方式</label>
                            <select id="open_mode" name="open_mode" class="form-input">
                                <option value="tab">新标签页</option>
                                <option value="window">新窗口</option>
                            </select>
                        </div>

                        <!-- 组合操作专用字段 -->
                        <div class="form-group" id="combo-url-group" style="display: none;">
                            <label class="form-label">网站链接 *</label>
                            <input type="url" id="combo_url" name="combo_url" class="form-input"
                                placeholder="例如: https://chat.openai.com">
                        </div>

                        <div class="form-group" id="combo-prompt-group" style="display: none;">
                            <label class="form-label">提示词内容 *</label>
                            <textarea id="combo_prompt" name="combo_prompt" class="form-input form-textarea"
                                placeholder="输入要自动填入的提示词内容..."></textarea>
                        </div>

                        <!-- 按钮组 -->
                        <div class="btn-group" style="justify-content: flex-end;">
                            <button type="submit" class="btn btn-secondary">
                                添加配置
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="reloadConfig()">
                                重载配置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 配置列表 -->
            <div class="card animate-slide-up">
                <div class="card-header">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <h2 class="card-title" style="margin-bottom: 0;">
                            ⚙️ 配置列表
                        </h2>
                        <!-- 筛选按钮组 -->
                        <div class="btn-group" id="filter-buttons">
                            <button class="btn btn-secondary btn-small filter-btn active" data-filter="all"
                                onclick="filterConfigs('all')">全部</button>
                            <button class="btn btn-secondary btn-small filter-btn" data-filter="prompt"
                                onclick="filterConfigs('prompt')">提示</button>
                            <button class="btn btn-secondary btn-small filter-btn" data-filter="url"
                                onclick="filterConfigs('url')">链接</button>
                            <button class="btn btn-secondary btn-small filter-btn" data-filter="combo"
                                onclick="filterConfigs('combo')">组合</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="config-container">
                        <div class="empty-state">
                            <h3>🔄 正在加载配置...</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script>
        // Socket.IO 连接
        const socket = io();

        // 状态元素
        const statusEl = document.getElementById('status');
        const alertContainer = document.getElementById('alert-container');
        const configContainer = document.getElementById('config-container');
        const addForm = document.getElementById('add-form');

        // 全局配置数据存储
        let allConfigs = [];
        let currentFilter = localStorage.getItem('configFilter') || 'all';

        // Socket 事件处理
        socket.on('connect', function () {
            statusEl.textContent = '✅ 已连接到服务器，快捷键监听中...';
            statusEl.className = 'status-indicator status-connected';
            loadConfig();
        });

        socket.on('disconnect', function () {
            statusEl.textContent = '❌ 与服务器断开连接';
            statusEl.className = 'status-indicator status-disconnected';
        });

        socket.on('status', function (data) {
            // 移除连接状态弹窗，因为右上角已有状态指示器
            // showAlert(data.message, 'success');
        });

        socket.on('error', function (data) {
            showAlert(data.message, 'error');
        });

        socket.on('config_updated', function (data) {
            loadConfig();
            showAlert('配置已更新', 'success');
        });

        // 显示提示信息（在导航栏中）
        function showAlert(message, type = 'success') {
            const navAlertContainer = document.getElementById('nav-alert-container');
            
            // 清除之前的提示
            navAlertContainer.innerHTML = '';
            
            // 创建新的提示元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `nav-alert nav-alert-${type}`;
            alertDiv.textContent = message;
            
            // 添加到导航栏
            navAlertContainer.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 加载配置
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const result = await response.json();

                if (result.success) {
                    renderConfig(result.data);
                } else {
                    showAlert('加载配置失败', 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 筛选配置
        function filterConfigs(filterType) {
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filterType}"]`).classList.add('active');

            // 保存用户选择
            currentFilter = filterType;
            localStorage.setItem('configFilter', filterType);

            // 筛选并渲染配置
            let filteredConfigs = allConfigs;
            if (filterType !== 'all') {
                filteredConfigs = allConfigs.filter(config => config.type === filterType);
            }

            renderFilteredConfig(filteredConfigs);
        }

        // 渲染筛选后的配置
        function renderFilteredConfig(configs) {
            if (configs.length === 0) {
                const filterText = currentFilter === 'all' ? '配置' :
                    currentFilter === 'prompt' ? '提示词配置' :
                        currentFilter === 'url' ? '网页链接配置' : '组合操作配置';

                configContainer.innerHTML = `
        <div class="empty-state">
            <h3>📝 暂无${filterText}</h3>
            <p>添加你的第一个${filterText}吧！</p>
        </div>
        `;
                return;
            }

            configContainer.innerHTML = configs.map((config, index) => {
                // 找到原始索引
                const originalIndex = allConfigs.findIndex(c =>
                    c.hotkey === config.hotkey && c.type === config.type && c.content === config.content
                );

                let typeText = '';
                let contentDisplay = '';
                let extraInfo = '';

                if (config.type === 'prompt') {
                    typeText = '提示词';
                    contentDisplay = config.content;
                } else if (config.type === 'url') {
                    typeText = '网页链接';
                    contentDisplay = config.content;
                    if (config.open_mode) {
                        extraInfo = `<div class="config-meta">打开方式: ${config.open_mode === 'window' ? '新窗口' : '新标签页'}</div>`;
                    }
                } else if (config.type === 'combo') {
                    typeText = '组合操作';
                    contentDisplay = `网站: ${config.combo_url || config.content}\n提示词: ${config.combo_prompt || ''}`;
                    extraInfo = `<div class="config-meta">
            打开方式: ${config.open_mode === 'window' ? '新窗口' : '新标签页'} |
            立即复制到剪贴板
        </div>`;
                }

                return `
        <div class="config-item" data-type="${config.type}">
            <!-- 第一行：快捷键 + 类型 + 描述 + 操作按钮 -->
            <div class="config-row-1">
                <div class="config-info">
                    <div class="config-hotkey">${config.hotkey}</div>
                    <span class="type-badge ${config.type}">${typeText}</span>
                    <div class="config-description">${config.description || '无描述'}</div>
                </div>
                <div class="config-actions">
                    <button class="btn btn-secondary btn-small" onclick="testConfig(${originalIndex})">测试</button>
                    <button class="btn btn-secondary btn-small" onclick="editConfig(${originalIndex})">编辑</button>
                    <button class="btn btn-secondary btn-small btn-danger"
                        onclick="deleteConfig(${originalIndex})">删除</button>
                </div>
            </div>
            <!-- 第二行：具体内容（固定显示） -->
            <div class="config-row-2">
                <div class="config-content">${contentDisplay}</div>
                ${extraInfo}
            </div>
        </div>
        `;
            }).join('');
        }

        // 修改原有的渲染函数
        function renderConfig(configs) {
            allConfigs = configs; // 保存所有配置数据

            // 恢复用户的筛选选择
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${currentFilter}"]`).classList.add('active');

            // 应用筛选
            filterConfigs(currentFilter);
        }

        // 类型选择变化处理
        document.getElementById('type').addEventListener('change', function () {
            const openModeGroup = document.getElementById('open-mode-group');
            const comboUrlGroup = document.getElementById('combo-url-group');
            const comboPromptGroup = document.getElementById('combo-prompt-group');
            const contentGroup = document.getElementById('content-group');
            const contentLabel = contentGroup.querySelector('label');
            const contentInput = document.getElementById('content');

            // 隐藏所有组合相关字段
            openModeGroup.style.display = 'none';
            comboUrlGroup.style.display = 'none';
            comboPromptGroup.style.display = 'none';

            if (this.value === 'url') {
                openModeGroup.style.display = 'block';
                contentLabel.textContent = '网页链接 *';
                contentInput.placeholder = '输入网页链接，例如: https://chat.openai.com';
                contentGroup.style.display = 'block';
            } else if (this.value === 'combo') {
                openModeGroup.style.display = 'block';
                comboUrlGroup.style.display = 'block';
                comboPromptGroup.style.display = 'block';
                contentGroup.style.display = 'none';
            } else {
                contentLabel.textContent = '内容 *';
                contentInput.placeholder = '输入提示词内容...';
                contentGroup.style.display = 'block';
            }
        });

        // 添加配置
        addForm.addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(addForm);
            const type = formData.get('type');

            const data = {
                hotkey: formData.get('hotkey').trim(),
                type: type,
                description: formData.get('description').trim()
            };

            // 根据类型处理不同的数据
            if (type === 'combo') {
                // 组合操作
                const comboUrl = formData.get('combo_url').trim();
                const comboPrompt = formData.get('combo_prompt').trim();

                if (!data.hotkey || !comboUrl || !comboPrompt) {
                    showAlert('快捷键、网站链接和提示词内容不能为空', 'error');
                    return;
                }

                data.content = comboUrl; // 主要内容是URL
                data.combo_url = comboUrl;
                data.combo_prompt = comboPrompt;
                data.combo_delay = 0; // 不延迟，立即复制到剪贴板
                data.open_mode = formData.get('open_mode') || 'tab';
            } else {
                // 普通操作
                data.content = formData.get('content').trim();

                if (!data.hotkey || !data.content) {
                    showAlert('快捷键和内容不能为空', 'error');
                    return;
                }

                // 只有URL类型才包含打开方式
                if (type === 'url') {
                    data.open_mode = formData.get('open_mode') || 'tab';
                }
            }

            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                    addForm.reset();
                    // 重置后隐藏所有组合相关选项
                    document.getElementById('open-mode-group').style.display = 'none';
                    document.getElementById('combo-url-group').style.display = 'none';
                    document.getElementById('combo-prompt-group').style.display = 'none';
                    document.getElementById('content-group').style.display = 'block';
                    loadConfig();
                } else {
                    showAlert(result.message, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        });

        // 测试配置
        async function testConfig(index) {
            try {
                const response = await fetch(`/api/test/${index}`, {
                    method: 'POST'
                });

                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 删除配置
        async function deleteConfig(index) {
            if (!confirm('确定要删除这个配置吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/config/${index}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                    loadConfig();
                } else {
                    showAlert(result.message, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 编辑配置（简化版，直接提示用户）
        function editConfig(index) {
            showAlert('编辑功能开发中，请先删除再重新添加', 'error');
        }

        // 重载配置
        async function reloadConfig() {
            try {
                const response = await fetch('/api/reload', {
                    method: 'POST'
                });

                const result = await response.json();
                showAlert(result.message, result.success ? 'success' : 'error');

                if (result.success) {
                    loadConfig();
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            }
        }

        // 快捷键录制功能
        let isRecording = false;
        let recordedKeys = new Set();

        function toggleRecording() {
            const recordBtn = document.getElementById('record-btn');
            const hotkeyInput = document.getElementById('hotkey');

            if (!isRecording) {
                // 开始录制
                isRecording = true;
                recordedKeys.clear();
                recordBtn.textContent = '⏹️';
                recordBtn.style.borderColor = 'var(--error)';
                recordBtn.style.color = 'var(--error)';
                hotkeyInput.value = '';
                hotkeyInput.placeholder = '请按下快捷键组合...';
                hotkeyInput.focus();
                showAlert('录制模式已开启，请按下想要的快捷键组合', 'success');
            } else {
                // 停止录制
                stopRecording();
            }
        }

        function stopRecording() {
            const recordBtn = document.getElementById('record-btn');
            const hotkeyInput = document.getElementById('hotkey');

            isRecording = false;
            recordBtn.textContent = '🎤';
            recordBtn.style.borderColor = '';
            recordBtn.style.color = '';
            hotkeyInput.placeholder = '例如: ctrl+shift+a';

            if (recordedKeys.size > 0) {
                showAlert('快捷键录制完成！', 'success');
            }
        }

        // 键盘事件监听
        document.addEventListener('keydown', function (e) {
            if (!isRecording) return;

            e.preventDefault();
            e.stopPropagation();

            const hotkeyInput = document.getElementById('hotkey');
            const keys = [];

            // 记录修饰键
            if (e.ctrlKey) keys.push('ctrl');
            if (e.altKey) keys.push('alt');
            if (e.shiftKey) keys.push('shift');
            if (e.metaKey) keys.push('meta');

            // 记录主键
            const mainKey = e.key.toLowerCase();
            if (mainKey !== 'control' && mainKey !== 'alt' && mainKey !== 'shift' && mainKey !== 'meta') {
                // 处理特殊键名
                let keyName = mainKey;
                if (mainKey === ' ') keyName = 'space';
                else if (mainKey === 'escape') keyName = 'esc';
                else if (mainKey.startsWith('arrow')) keyName = mainKey.replace('arrow', '');
                else if (mainKey.startsWith('f') && /^f\d+$/.test(mainKey)) keyName = mainKey;
                else if (mainKey.length === 1) keyName = mainKey;

                keys.push(keyName);

                // 更新输入框
                const hotkeyString = keys.join('+');
                hotkeyInput.value = hotkeyString;

                // 自动停止录制（当按下非修饰键时）
                setTimeout(() => {
                    stopRecording();
                }, 100);
            }
        });

        // 防止录制时的默认行为
        document.addEventListener('keyup', function (e) {
            if (isRecording) {
                e.preventDefault();
                e.stopPropagation();
            }
        });

        // 点击其他地方时停止录制
        document.addEventListener('click', function (e) {
            if (isRecording && !e.target.closest('#record-btn') && !e.target.closest('#hotkey')) {
                stopRecording();
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 如果已经连接，立即加载配置
            if (socket.connected) {
                loadConfig();
            }
        });
    </script>
</body>

</html>