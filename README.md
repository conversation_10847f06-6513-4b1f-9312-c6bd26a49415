# 🚀 HotKey AI - 快捷键提示词助手

一个高效的全局快捷键工具，支持快速复制提示词和打开网页，专为 AI 使用场景优化。现在支持现代化的 Web 界面！

## ✨ 功能特性

- 🎯 **全局快捷键**: 在任何应用中都能响应快捷键
- 📋 **提示词复制**: 一键复制预设的 AI 提示词到剪贴板
- 🌐 **网页快启**: 快速打开常用的 AI 网站
- 🎨 **现代化界面**: 美观的 Web 界面，支持实时更新
- 💾 **数据持久化**: 配置自动保存到本地 JSON 文件
- 🔄 **热重载**: 修改配置后自动生效
- 📱 **响应式设计**: 支持各种屏幕尺寸

## 🚀 快速开始

### 一键启动（推荐）
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动 Web 界面（默认）
python main.py
```
程序会自动打开浏览器访问 http://127.0.0.1:5000

### 其他启动方式
```bash
# 或使用专用启动脚本
python start_web.py    # Web 版本
python install.py      # 一键安装并启动
```

## 🌐 Web 界面特色

- 🎨 **现代化设计**: 渐变背景 + 卡片式布局
- 📱 **响应式界面**: 完美适配手机、平板、电脑
- ⚡ **实时同步**: WebSocket 实时更新配置状态
- 🔔 **智能提示**: 操作成功/失败的即时反馈
- 🎯 **直观操作**: 一键测试、编辑、删除功能

### 环境要求
- Python 3.7+
- Windows/macOS/Linux
- 现代浏览器（Chrome、Firefox、Safari、Edge）

## 📋 默认快捷键

| 快捷键 | 功能 | 内容 |
|--------|------|------|
| `Ctrl+Shift+1` | 提示词 | 请帮我用一句话总结下面内容： |
| `Ctrl+Shift+2` | 提示词 | 请帮我翻译成中文： |
| `Ctrl+Shift+3` | 网页 | 打开 ChatGPT |
| `Ctrl+Shift+4` | 提示词 | 请帮我优化这段代码，提高可读性和性能： |
| `Ctrl+Shift+5` | 网页 | 打开 Claude |

## 🎮 使用方法

### Web 界面操作（默认）
1. **启动程序**: 运行 `python main.py`，浏览器会自动打开
2. **添加快捷键**: 点击"➕ 添加快捷键"按钮
3. **编辑配置**: 点击配置项右侧的"✏️ 编辑"按钮
4. **测试功能**: 点击"🧪 测试"按钮验证配置
5. **实时更新**: 所有修改会实时同步，无需重启

### 快捷键使用
1. **提示词模式**: 按快捷键自动复制到剪贴板，然后粘贴到 AI 对话框
2. **网页模式**: 按快捷键自动打开指定网页
3. **全局响应**: 在任何应用中都能响应快捷键

### 界面特色
- 🎨 **现代化设计**: 渐变背景 + 卡片式布局
- 📱 **响应式界面**: 完美适配手机、平板、电脑
- ⚡ **实时同步**: WebSocket 实时更新配置状态
- 🔔 **智能提示**: 操作成功/失败的即时反馈
- 🎯 **直观操作**: 一键测试、编辑、删除功能

## ⚙️ 配置说明

### 快捷键格式
- 基本格式: `ctrl+shift+1`, `alt+f1`, `ctrl+alt+a`
- 支持修饰键: `ctrl`, `alt`, `shift`
- 支持按键: 字母、数字、功能键

### 配置文件
配置保存在 `config.json` 文件中，格式如下：
```json
[
  {
    "hotkey": "ctrl+shift+1",
    "type": "prompt",
    "content": "请帮我用一句话总结下面内容：",
    "description": "内容总结"
  },
  {
    "hotkey": "ctrl+shift+2",
    "type": "url",
    "content": "https://chat.openai.com",
    "description": "打开ChatGPT"
  }
]
```

## 🔧 高级功能

### 打包为可执行文件
```bash
pip install pyinstaller
pyinstaller --noconfirm --onefile --windowed main.py
```

### 开机自启动
将生成的 exe 文件添加到系统启动项中。

## 💡 使用技巧

1. **避免快捷键冲突**: 选择不与系统或其他软件冲突的快捷键
2. **提示词优化**: 根据使用场景定制专属的提示词模板
3. **分组管理**: 使用描述字段对快捷键进行分类
4. **测试功能**: 使用 Web 界面中的"测试"按钮验证配置

## 🐛 常见问题

### Q: 快捷键不响应？
A: 检查是否与其他软件快捷键冲突，尝试更换快捷键组合。

### Q: 程序崩溃？
A: 检查 `config.json` 文件格式是否正确，或删除该文件使用默认配置。

### Q: 无法复制到剪贴板？
A: 确保安装了 `pyperclip` 库，某些 Linux 系统可能需要额外配置。

## 📝 更新日志

### v1.0.0 (MVP)
- ✅ 基础快捷键功能
- ✅ Web 配置界面
- ✅ 提示词和网页支持
- ✅ 配置文件持久化
- ✅ 自动依赖检查和安装

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License